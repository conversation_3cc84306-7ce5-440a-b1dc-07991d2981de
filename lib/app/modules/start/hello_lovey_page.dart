import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:lovey_app/app/core/theme/colors.dart';
import 'package:lovey_app/app/core/theme/fonts.enum.dart';
import 'package:lovey_app/app/core/util/assets_strings.dart';
import 'package:lovey_app/app/core/util/get_text_style.dart';
import 'package:lovey_app/app/modules/start/controller/hello_lovey_controller.dart';
import 'package:lovey_app/app/widget/background_container.dart';
import 'package:lovey_app/app/widget/button.dart';
import 'package:lovey_app/app/widget/custom_image.dart';
import 'package:sizer/sizer.dart';

class HelloLoveyPage extends StatefulWidget {
  const HelloLoveyPage({super.key});

  @override
  State<HelloLoveyPage> createState() => _HelloLoveyPageState();
}

class _HelloLoveyPageState extends State<HelloLoveyPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<double> _newTextSlideAnimation;
  late Animation<double> _newTextFadeAnimation;
  late Animation<double> _loveyToAppBarAnimation;
  late Animation<double> _loveyToAppBarFadeAnimation;
  late Animation<double> _loveyToAppBarScaleAnimation;
  HelloLoveyController pageController = HelloLoveyController();

  // Controla qual imagem mostrar
  String _currentImage = Assets.svg.bigLoveyNormal;

  // Controla a visibilidade dos textos
  bool _showOriginalText = true;
  bool _showNewText = false;
  bool _showLoveyInAppBar = false;

  @override
  void initState() {
    super.initState();

    // Inicializa o controller da animação
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800), // Duração da animação
      vsync: this,
    );

    // Cria a animação de deslizamento
    _slideAnimation = Tween<double>(
      begin: 1.0, // Começa fora da tela (abaixo)
      end: 0.0, // Termina na posição normal
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // Curva suave com um pequeno bounce
      ),
    );

    // Cria a animação de fade (opacidade)
    _fadeAnimation = Tween<double>(
      begin: 0.0, // Começa invisível
      end: 1.0, // Termina totalmente visível
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn, // Fade suave
      ),
    );

    // Cria a animação de escala para o crescimento
    _scaleAnimation = Tween<double>(
      begin: 1.0, // Tamanho normal
      end: 1.0, // Será alterado quando necessário
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Animação de fade out para o texto original
    _textFadeAnimation = Tween<double>(
      begin: 1.0, // Visível
      end: 1.0, // Invisível
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // Inicializa as animações do novo texto com valores que mantêm o texto invisível
    _newTextSlideAnimation = Tween<double>(
      begin: 1.0, // Começa abaixo (fora da tela)
      end: 1.0, // Mantém fora da tela inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    // Animação de fade in para o novo texto (começa e mantém invisível)
    _newTextFadeAnimation = Tween<double>(
      begin: 0.0, // Invisível
      end: 0.0, // Mantém invisível inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    // Inicializa as animações para mover "lovey" para a AppBar
    _loveyToAppBarAnimation = Tween<double>(
      begin: -0.15, // Posição inicial (centro)
      end: -0.15, // Mantém na posição inicial
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loveyToAppBarFadeAnimation = Tween<double>(
      begin: 0.0, // Invisível
      end: 0.0, // Mantém invisível inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _loveyToAppBarScaleAnimation = Tween<double>(
      begin: 1.0, // Tamanho normal
      end: 1.0, // Mantém tamanho normal inicialmente
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Inicia a animação após 3 segundos
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _animationController.forward();
        _startImageSequence();
      }
    });
  }

  void _startImageSequence() {
    // Após 1 segundo: muda para bigLoveySmiling
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _currentImage = Assets.svg.bigLoveySmiling;
        });
      }
    });

    // Após mais 1 segundo (2 segundos total): muda para bigLoveyContent e inicia animação de texto
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _currentImage = Assets.svg.bigLoveyContent;
        });
        _startTextAnimation();
      }
    });

    // Após mais 3 segundos (4 segundos total): muda para biggerLoveyNormal com crescimento
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        setState(() {
          _currentImage = Assets.svg.biggerLoveyNormal;
        });
        _startGrowthAnimation();
      }
    });

    // Após mais 2 segundos (6 segundos total): inicia animação do "lovey" para AppBar
    Future.delayed(const Duration(seconds: 6), () {
      if (mounted) {
        _startLoveyToAppBarAnimation();
      }
    });
  }

  void _startTextAnimation() {
    // Fade out rápido do texto original
    final textFadeController = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    );

    final textFadeOut = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: textFadeController, curve: Curves.easeOut),
    );

    textFadeOut.addListener(() {
      setState(() {
        _textFadeAnimation = textFadeOut;
      });
    });

    textFadeController.forward().then((_) {
      // Após o fade out, esconde o texto original
      setState(() {
        _showOriginalText = false;
      });

      // Pequeno delay antes de mostrar o novo texto para evitar o "piscar"
      Future.delayed(const Duration(milliseconds: 50), () {
        if (mounted) {
          setState(() {
            _showNewText = true;
          });
          // Inicia a animação do novo texto
          _startNewTextAnimation();
        }
      });

      textFadeController.dispose();
    });
  }

  void _startNewTextAnimation() {
    // Animação do novo texto subindo
    final newTextController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    final slideAnimation = Tween<double>(
      begin: 1.0, // Começa abaixo (fora da tela)
      end: -0.15, // Termina 15% acima da posição central
    ).animate(
      CurvedAnimation(parent: newTextController, curve: Curves.easeOutBack),
    );

    final fadeAnimation = Tween<double>(
      begin: 0.0, // Invisível
      end: 1.0, // Visível
    ).animate(
      CurvedAnimation(
        parent: newTextController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );

    // Atualiza as animações antes de iniciar
    setState(() {
      _newTextSlideAnimation = slideAnimation;
      _newTextFadeAnimation = fadeAnimation;
    });

    // Adiciona listener para atualizar o estado durante a animação
    newTextController.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });

    newTextController.forward().then((_) {
      newTextController.dispose();
    });
  }

  void _startGrowthAnimation() {
    // Cria uma nova animação de escala para o crescimento
    final growthController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    final growthAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3, // Cresce 30% em altura para compensar a diferença de tamanho
    ).animate(
      CurvedAnimation(parent: growthController, curve: Curves.easeOutBack),
    );

    // Atualiza a animação de escala
    _scaleAnimation = growthAnimation;

    growthController.forward();

    // Limpa o controller após a animação
    growthController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        growthController.dispose();
      }
    });
  }

  void _startLoveyToAppBarAnimation() {
    // Primeiro, mostra o "lovey" na posição original
    setState(() {
      _showLoveyInAppBar = true;
    });

    // Pequeno delay para garantir que o widget seja renderizado
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!mounted) return;

      // Cria um controller específico para a animação do "lovey" para AppBar
      final loveyToAppBarController = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );

      // Calcula a distância do centro da tela até a posição da AppBar
      final screenHeight = MediaQuery.of(context).size.height;
      final appBarHeight = kToolbarHeight + MediaQuery.of(context).padding.top;

      // Posição inicial: centro da tela (onde está o texto "lovey" original)
      final centerY = screenHeight / 2;
      // Posição final: AppBar
      final appBarY = appBarHeight + 16; // 16px de padding
      final moveDistance = centerY - appBarY;

      final slideToAppBarAnimation = Tween<double>(
        begin: 0.0, // Posição inicial (centro)
        end: -moveDistance, // Move para cima até a AppBar
      ).animate(
        CurvedAnimation(
          parent: loveyToAppBarController,
          curve: Curves.easeInOutBack,
        ),
      );

      final fadeToAppBarAnimation = Tween<double>(
        begin: 1.0, // Visível
        end: 1.0, // Mantém visível durante a animação
      ).animate(
        CurvedAnimation(parent: loveyToAppBarController, curve: Curves.easeIn),
      );

      final scaleToAppBarAnimation = Tween<double>(
        begin: 1.0, // Tamanho original (66.18)
        end: 0.48, // Tamanho da AppBar (32 / 66.18 ≈ 0.48)
      ).animate(
        CurvedAnimation(
          parent: loveyToAppBarController,
          curve: Curves.easeInOut,
        ),
      );

      // Atualiza as animações
      setState(() {
        _loveyToAppBarAnimation = slideToAppBarAnimation;
        _loveyToAppBarFadeAnimation = fadeToAppBarAnimation;
        _loveyToAppBarScaleAnimation = scaleToAppBarAnimation;
      });

      // Adiciona listener para atualizar o estado durante a animação
      loveyToAppBarController.addListener(() {
        if (mounted) {
          setState(() {});
        }
      });

      loveyToAppBarController.forward().then((_) {
        loveyToAppBarController.dispose();
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget getButtonChild({String social = 'google'}) {
    return Container(
      height: 34,
      margin: const EdgeInsets.only(left: 10),
      // width: 245,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomImage(
              'assets/img/png/${social.toLowerCase()}_logo.png',
              width: 32,
            ),
            const Spacer(),
            Text(
              'SIGN IN WITH ${social.toUpperCase()} ACCOUNT',
              style: buttonDefaultTextStyle(
                Colors.black,
                fontSize: 13,
                letterSpacing: 1,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }

  Widget getButton(
    Function() onPressed,
    String label, {
    Color textColor = Colors.white,
    Color color = Colors.white,
    Color borderColor = Colors.white,
    bool outlined = false,
    Widget? child,
  }) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(maxWidth: 311),
      height: 51,
      child: Button(
        onPressed: onPressed,
        outlined: outlined,
        autoSized: true,
        text: label,
        fontSize: 12,
        textColor: textColor,
        color: color,
        borderColor: borderColor,
        child: child,
      ),
    );
  }

  Widget _buildSocialButtons() {
    return Column(
      children: [
        getButton(
          pageController.handleGoogleSignIn,
          'GOOGLE',
          color: Colors.black,
          textColor: Colors.white,
          child: getButtonChild(),
          borderColor: Colors.transparent,
        ),
        const SizedBox(height: 10),
        getButton(
          pageController.handleAppleSingIn,
          'APPLE',
          color: Colors.white,
          textColor: Colors.white,
          child: getButtonChild(social: 'apple'),
          borderColor: Colors.transparent,
        ),
      ],
    );
  }

  Widget _buildCenterWidgets() {
    return Column(
      children: [
        // Texto original "Hey There!"
        if (_showOriginalText)
          Align(
            alignment: Alignment.center,
            child: AnimatedBuilder(
              animation: _textFadeAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _textFadeAnimation.value,
                  child: Center(
                    child: Text(
                      'Hey There!',
                      style: getTextStyle(
                        45,
                        fontWeight: FontWeight.w400,
                        fontFamily: TextFontEnum.caprasimo,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        // Novo texto "I'm Lovey" (por trás da imagem)
        if (_showNewText)
          Align(
            alignment: Alignment.center,
            child: AnimatedBuilder(
              animation: _newTextSlideAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    0,
                    _newTextSlideAnimation.value * 100.h * 0.15,
                  ),
                  child: Opacity(
                    opacity: _newTextFadeAnimation.value,
                    child: Center(
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children:
                              !_showLoveyInAppBar
                                  ? [
                                    // Texto "I'm"
                                    TextSpan(
                                      text: "I'm\n",
                                      style: getTextStyle(
                                        45,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: TextFontEnum.caprasimo,
                                        height: -0.5,
                                      ),
                                    ),
                                    TextSpan(
                                      text: 'lovey',
                                      style: getTextStyle(
                                        66.18,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: TextFontEnum.caprasimo,
                                      ),
                                    ),
                                  ]
                                  : [
                                    TextSpan(
                                      text: "Got feels? Ask me!",
                                      style: getTextStyle(
                                        25,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: TextFontEnum.poppins,
                                        height: -0.5,
                                      ),
                                    ),
                                  ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        // "lovey" animado para a AppBar
        if (_showLoveyInAppBar)
          AnimatedBuilder(
            animation: _loveyToAppBarAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, _loveyToAppBarAnimation.value),
                child: Transform.scale(
                  scale: _loveyToAppBarScaleAnimation.value,
                  child: Opacity(
                    opacity: _loveyToAppBarFadeAnimation.value,
                    child: Align(
                      alignment: Alignment.center, // Começa no centro
                      child: Text(
                        'lovey',
                        style: getTextStyle(
                          66.18, // Mesmo tamanho do original inicialmente
                          fontWeight: FontWeight.w400,
                          fontFamily: TextFontEnum.caprasimo,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(
                0,
                _slideAnimation.value * MediaQuery.of(context).size.height,
              ),
              child: Opacity(
                opacity: _fadeAnimation.value,
                child: Transform.scale(
                  scaleY: _scaleAnimation.value,
                  alignment: Alignment.bottomCenter,
                  child: Align(
                    alignment: Alignment.bottomCenter,
                    child: CustomImage(_currentImage),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildFinalCenterBody() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Sign in to begin!',
          style: getTextStyle(
            30,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.5,
            fontFamily: TextFontEnum.poppins,
          ),
        ),
        SizedBox(height: 7.05.h),
        Center(
          // width: 300,
          child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              text: 'By signing in, you agree to our ',
              style: getTextStyle(
                14,

                letterSpacing: -0.4,
                fontWeight: FontWeight.w400,
                fontFamily: TextFontEnum.poppins,
              ),
              children: <TextSpan>[
                TextSpan(
                  text: 'Terms of Use\n',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.bold,
                    fontFamily: TextFontEnum.poppins,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          // Ação quando o texto "TERMS OF USE" for clicado
                          pageController.launchPageOfTermsOfUse();
                        },
                ),
                TextSpan(
                  text: 'and ',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.w400,
                    fontFamily: TextFontEnum.poppins,
                  ),
                ),
                TextSpan(
                  text: 'Privacy Policy',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.bold,
                    fontFamily: TextFontEnum.poppins,
                    decoration: TextDecoration.underline,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          pageController.launchPageOfPrivacyPolicy();
                        },
                ),
                TextSpan(
                  text: ' on data and cookies.',
                  style: getTextStyle(
                    14,

                    letterSpacing: -0.4,
                    fontWeight: FontWeight.w400,
                    fontFamily: TextFontEnum.poppins,
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.53.h),
        _buildSocialButtons(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BackgroundContainer(
        child: Stack(children: [_buildFinalCenterBody()]),
      ),
    );
  }
}
